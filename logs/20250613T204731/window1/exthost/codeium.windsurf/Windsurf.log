2025-06-13 20:47:35.144 [info] 2025/06/13 20:47:35 maxprocs: Leaving GOMAXPROCS=8: CPU quota undefined
2025-06-13 20:47:35.419 [info] I0613 20:47:35.419700 95558 main.go:602] Setting GOMAXPROCS to 4
2025-06-13 20:47:35.420 [info] I0613 20:47:35.420921 95558 main.go:817] Starting language server process with pid 95558
2025-06-13 20:47:35.422 [info] I0613 20:47:35.421997 95558 server.go:292] Language server will attempt to listen on host 127.0.0.1
2025-06-13 20:47:35.422 [info] I0613 20:47:35.422657 95558 server.go:299] Language server listening on random port at 49802
2025-06-13 20:47:36.214 [info] I0613 20:47:36.214680 95558 server.go:420] Created extension server client at port 49799
2025-06-13 20:47:36.361 [info] I0613 20:47:36.361325 95558 server.go:948] Local search is enabled, will index local files.
2025-06-13 20:47:36.361 [info] I0613 20:47:36.361343 95558 server.go:952] Using 2 indexer workers
2025-06-13 20:47:38.102 [info] (Windsurf) 2025-06-13 20:47:38.100 [INFO]: Language server started
2025-06-13 20:47:38.387 [info] (Windsurf) 2025-06-13 20:47:38.387 [INFO]: LS lspClient started successfully
2025-06-13 20:48:16.318 [info] Downloading Chromium 133.0.6943.16 (playwright build v1155) from https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1155/chromium-mac-arm64.zip

2025-06-13 20:48:47.124 [info] Error: Request to https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1155/chromium-mac-arm64.zip timed out after 30000ms
2025-06-13 20:48:47.124 [info]     at ClientRequest.rejectOnTimeout (/Users/<USER>/Library/Caches/ms-playwright-go/1.50.1/package/lib/utils/network.js:76:15)
2025-06-13 20:48:47.125 [info]     at Object.onceWrapper (node:events:638:28)
2025-06-13 20:48:47.125 [info]     at ClientRequest.emit (node:events:524:28)
2025-06-13 20:48:47.125 [info]     at TLSSocket.emitRequestTimeout (node:_http_client:863:9)
2025-06-13 20:48:47.125 [info]     at Object.onceWrapper (node:events:638:28)
2025-06-13 20:48:47.125 [info]     at TLSSocket.emit (node:events:536:35)
2025-06-13 20:48:47.125 [info]     at Socket._onTimeout (node:net:609:8)
2025-06-13 20:48:47.125 [info]     at listOnTimeout (node:internal/timers:594:17)
2025-06-13 20:48:47.125 [info]     at process.processTimers (node:internal/timers:529:7)
2025-06-13 20:48:47.129 [info] Downloading Chromium 133.0.6943.16 (playwright build v1155) from https://playwright.download.prss.microsoft.com/dbazure/download/playwright/builds/chromium/1155/chromium-mac-arm64.zip

2025-06-13 20:50:02.248 [info] Error: connect ETIMEDOUT ************:443
2025-06-13 20:50:02.249 [info]     at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1634:16) {
2025-06-13 20:50:02.249 [info]   errno: -60,
2025-06-13 20:50:02.249 [info]   code: 'ETIMEDOUT',
2025-06-13 20:50:02.249 [info]   syscall: 'connect',
2025-06-13 20:50:02.249 [info]   address: '************',
2025-06-13 20:50:02.249 [info]   port: 443
2025-06-13 20:50:02.249 [info] }
2025-06-13 20:50:02.254 [info] Downloading Chromium 133.0.6943.16 (playwright build v1155) from https://cdn.playwright.dev/builds/chromium/1155/chromium-mac-arm64.zip

2025-06-13 20:50:02.892 [info] |                                                                                |   0% of 123.3 MiB

2025-06-13 20:53:48.964 [info] |■■■■■■■■                                                                        |  10% of 123.3 MiB

2025-06-13 20:57:28.908 [info] |■■■■■■■■■■■■■■■■                                                                |  20% of 123.3 MiB

2025-06-13 21:01:46.407 [info] |■■■■■■■■■■■■■■■■■■■■■■■■                                                        |  30% of 123.3 MiB

2025-06-13 21:01:47.361 [info] E0613 21:01:47.360406 95558 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 21:01:47.657 [info] E0613 21:01:47.657088 95558 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 21:01:48.667 [info] E0613 21:01:48.667646 95558 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 21:01:49.675 [info] E0613 21:01:49.675006 95558 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 21:01:50.684 [info] E0613 21:01:50.683887 95558 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 21:01:51.693 [info] E0613 21:01:51.693012 95558 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
