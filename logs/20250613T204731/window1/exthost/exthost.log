2025-06-13 20:47:33.117 [info] Extension host with pid 95541 started
2025-06-13 20:47:33.139 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-13 20:47:33.156 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-13 20:47:33.158 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-13 20:47:33.181 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-13 20:47:33.238 [info] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-06-13 20:47:38.117 [info] Eager extensions activated
2025-06-13 20:47:38.123 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 20:47:38.125 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 20:47:38.136 [info] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 20:47:38.139 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 20:47:38.299 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:01:52.504 [info] Extension host terminating: renderer closed the MessagePort
2025-06-13 21:01:52.521 [info] Extension host with pid 95541 exiting with code 0
