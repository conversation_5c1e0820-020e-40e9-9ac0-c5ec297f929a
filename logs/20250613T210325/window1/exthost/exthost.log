2025-06-13 21:03:27.039 [info] Extension host with pid 2833 started
2025-06-13 21:03:27.046 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-13 21:03:27.063 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-13 21:03:27.065 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-13 21:03:27.088 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-13 21:03:27.147 [info] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-06-13 21:03:31.515 [info] Eager extensions activated
2025-06-13 21:03:31.520 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:03:31.522 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:03:31.533 [info] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:03:31.538 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:03:31.694 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:03:39.386 [info] Extension host terminating: renderer closed the MessagePort
2025-06-13 21:03:39.408 [info] Extension host with pid 2833 exiting with code 0
