2025-06-13 21:03:26.296 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.296 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.296 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.296 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.296 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.297 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.297 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.297 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.297 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.297 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:03:26.414 [warning] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-06-13 21:03:26.619 [info] Started local extension host with pid 2833.
2025-06-13 21:03:26.633 [error] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-06-13 21:03:26.648 [warning] [ms-python.python]: Cannot register 'python.venvPath'. This property is already registered.
2025-06-13 21:03:29.128 [info] [perf] Render performance baseline is 13ms
2025-06-13 21:03:35.450 [error] [Extension Host] Error during login process: ConnectError: [permission_denied] api server wire error: free user account exceeded, please use an existing account or upgrade to a paid plan
	at c (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856441)
	at l (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856837)
	at next (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3863488)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3462828
	at async Object.unary (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3861824)
	at async Object.getUserStatus (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3849687)
	at async v (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2282983)
	at async t.initializeAuthSession (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2282205)
