2025-06-13 20:45:02.427 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 20:45:02.428 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 20:45:02.804 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.csdevkit-1.16.6-darwin-arm64/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.csdevkit-1.16.6-darwin-arm64/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 20:45:03.768 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.csharp-2.63.32-darwin-arm64/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/ms-dotnettools.csharp-2.63.32-darwin-arm64/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async qu.scanExistingExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:34393)
    at async vh.scanUserExtensionAtLocation (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67602)
    at async wh.Bb (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:65806)
2025-06-13 20:45:06.616 [info] Started initializing default profile extensions in extensions installation folder. file:///Users/<USER>/.windsurf/extensions
2025-06-13 20:45:06.617 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async Promise.all (index 0)
    at async da.r (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:40624)
    at async qu.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33645)
    at async file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:35585
    at async qu.scanUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33306)
    at async Promise.all (index 1)
    at async qu.scanAllExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:32552)
    at async vh.scanExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67134)
    at async $2.call (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:21678)
2025-06-13 20:45:06.655 [info] Completed initializing default profile extensions in extensions installation folder. file:///Users/<USER>/.windsurf/extensions
