2025-06-13 20:43:58.234 [info] 2025/06/13 20:43:58 maxprocs: Leaving GOMAXPROCS=8: CPU quota undefined
2025-06-13 20:43:58.360 [info] I0613 20:43:58.360251 92919 main.go:602] Setting GOMAXPROCS to 4
2025-06-13 20:43:58.360 [info] I0613 20:43:58.360623 92919 main.go:817] Starting language server process with pid 92919
2025-06-13 20:43:58.360 [info] I0613 20:43:58.360654 92919 server.go:292] Language server will attempt to listen on host 127.0.0.1
2025-06-13 20:43:58.360 [info] I0613 20:43:58.360754 92919 server.go:299] Language server listening on random port at 49240
2025-06-13 20:43:59.238 [info] I0613 20:43:59.238606 92919 server.go:420] Created extension server client at port 49239
2025-06-13 20:43:59.382 [info] I0613 20:43:59.382449 92919 server.go:948] Local search is enabled, will index local files.
2025-06-13 20:43:59.382 [info] I0613 20:43:59.382466 92919 server.go:952] Using 2 indexer workers
2025-06-13 20:44:00.055 [info] (Windsurf) 2025-06-13 20:44:00.052 [INFO]: Language server started
2025-06-13 20:44:00.103 [info] (Windsurf) 2025-06-13 20:44:00.103 [INFO]: LS lspClient started successfully
2025-06-13 20:44:29.281 [info] Downloading Chromium 133.0.6943.16 (playwright build v1155) from https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1155/chromium-mac-arm64.zip

2025-06-13 20:45:00.137 [info] Error: Request to https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1155/chromium-mac-arm64.zip timed out after 30000ms
2025-06-13 20:45:00.138 [info]     at ClientRequest.rejectOnTimeout (/Users/<USER>/Library/Caches/ms-playwright-go/1.50.1/package/lib/utils/network.js:76:15)
2025-06-13 20:45:00.138 [info]     at Object.onceWrapper (node:events:638:28)
2025-06-13 20:45:00.138 [info]     at ClientRequest.emit (node:events:524:28)
2025-06-13 20:45:00.138 [info]     at TLSSocket.emitRequestTimeout (node:_http_client:863:9)
2025-06-13 20:45:00.138 [info]     at Object.onceWrapper (node:events:638:28)
2025-06-13 20:45:00.138 [info]     at TLSSocket.emit (node:events:536:35)
2025-06-13 20:45:00.138 [info]     at Socket._onTimeout (node:net:609:8)
2025-06-13 20:45:00.138 [info]     at listOnTimeout (node:internal/timers:594:17)
2025-06-13 20:45:00.138 [info]     at process.processTimers (node:internal/timers:529:7)
2025-06-13 20:45:00.144 [info] Downloading Chromium 133.0.6943.16 (playwright build v1155) from https://playwright.download.prss.microsoft.com/dbazure/download/playwright/builds/chromium/1155/chromium-mac-arm64.zip

2025-06-13 20:45:02.196 [info] (Windsurf) 2025-06-13 20:45:02.196 [ERROR]: Failed to export VSCode extensions: Error: Invalid package /Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/lib/app.asar
2025-06-13 20:45:04.489 [info] E0613 20:45:04.489528 92919 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:04.772 [info] E0613 20:45:04.772168 92919 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:05.778 [info] E0613 20:45:05.777936 92919 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:08.758 [info] 2025/06/13 20:45:08 maxprocs: Leaving GOMAXPROCS=8: CPU quota undefined
2025-06-13 20:45:08.863 [info] I0613 20:45:08.863383 93428 main.go:602] Setting GOMAXPROCS to 4
2025-06-13 20:45:08.863 [info] I0613 20:45:08.863609 93428 main.go:817] Starting language server process with pid 93428
2025-06-13 20:45:08.863 [info] I0613 20:45:08.863704 93428 server.go:292] Language server will attempt to listen on host 127.0.0.1
2025-06-13 20:45:08.863 [info] I0613 20:45:08.863797 93428 server.go:299] Language server listening on random port at 49429
2025-06-13 20:45:10.010 [info] I0613 20:45:10.009751 93428 server.go:420] Created extension server client at port 49427
2025-06-13 20:45:10.172 [info] I0613 20:45:10.172600 93428 server.go:948] Local search is enabled, will index local files.
2025-06-13 20:45:10.172 [info] I0613 20:45:10.172622 93428 server.go:952] Using 2 indexer workers
2025-06-13 20:45:10.863 [info] (Windsurf) 2025-06-13 20:45:10.860 [INFO]: Language server started
2025-06-13 20:45:11.149 [info] (Windsurf) 2025-06-13 20:45:11.149 [INFO]: LS lspClient started successfully
2025-06-13 20:45:16.767 [info] E0613 20:45:16.767719 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:17.040 [info] E0613 20:45:17.040736 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:18.049 [info] E0613 20:45:18.049182 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:19.077 [info] E0613 20:45:19.076950 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:20.091 [info] E0613 20:45:20.091118 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:21.100 [info] E0613 20:45:21.099750 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:22.111 [info] E0613 20:45:22.109950 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:23.121 [info] E0613 20:45:23.119100 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:24.126 [info] E0613 20:45:24.125948 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:25.139 [info] E0613 20:45:25.139476 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:26.147 [info] E0613 20:45:26.146379 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:27.161 [info] E0613 20:45:27.158581 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:28.167 [info] E0613 20:45:28.167245 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:29.176 [info] E0613 20:45:29.175769 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:30.621 [info] E0613 20:45:30.621163 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:31.630 [info] E0613 20:45:31.629844 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:32.635 [info] E0613 20:45:32.635360 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:33.641 [info] E0613 20:45:33.641689 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:34.652 [info] E0613 20:45:34.648570 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:35.657 [info] E0613 20:45:35.656988 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
2025-06-13 20:45:36.665 [info] E0613 20:45:36.665007 93428 interceptor.go:73] /exa.language_server_pb.LanguageServerService/StreamCascadePanelReactiveUpdates (unknown): reactive component b1cdf25d-7608-4035-abcd-bb373618f913 not found
