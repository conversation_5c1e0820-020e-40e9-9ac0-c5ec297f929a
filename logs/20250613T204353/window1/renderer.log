2025-06-13 20:43:53.932 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.943 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.943 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.943 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.943 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.943 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.944 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.944 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.944 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:53.944 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 20:43:54.052 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-06-13 20:43:54.056 [warning] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-06-13 20:43:54.103 [error] Cannot read properties of undefined (reading 'label'): TypeError: Cannot read properties of undefined (reading 'label')
    at _ce.eb (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:844:11629)
    at _ce.db (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:844:9734)
    at _ce.ab (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:844:8805)
2025-06-13 20:43:54.141 [info] Started initializing default profile extensions in extensions installation folder. file:///Users/<USER>/.windsurf/extensions
2025-06-13 20:43:54.245 [info] Completed initializing default profile extensions in extensions installation folder. file:///Users/<USER>/.windsurf/extensions
2025-06-13 20:43:54.399 [info] Started local extension host with pid 92513.
2025-06-13 20:43:58.050 [info] [perf] Render performance baseline is 24ms
2025-06-13 20:45:03.441 [error] [Extension Host] Error during login process: ConnectError: [permission_denied] api server wire error: free user account exceeded, please use an existing account or upgrade to a paid plan
	at c (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856441)
	at l (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856837)
	at next (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3863488)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3462828
	at async Object.unary (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3861824)
	at async Object.getUserStatus (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3849687)
	at async v (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2282983)
	at async t.initializeAuthSession (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2282205)
2025-06-13 20:45:06.689 [error] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-06-13 20:45:06.759 [info] Started local extension host with pid 93411.
2025-06-13 20:45:06.795 [error] Canceled: Canceled
    at new CPn (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:3406:35664)
    at S9e.U (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:3406:40316)
    at n.<computed>.r.charCodeAt.n.<computed> (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:3406:37801)
    at rjs.getSessions (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1321:66970)
    at Sbe.getSessions (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1321:65680)
    at pve.Rb (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1750:35364)
    at vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1750:33676
    at Array.map (<anonymous>)
    at pve.Mb (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1750:33664)
    at vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1750:33571
2025-06-13 20:45:06.887 [error] Canceled: Canceled
    at new CPn (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:3406:35664)
    at S9e.U (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:3406:40316)
    at n.<computed>.r.charCodeAt.n.<computed> (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:3406:37801)
    at rjs.getSessions (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1321:66970)
    at Sbe.getSessions (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1321:65680)
    at pve.Rb (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1750:35364)
    at vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1750:33676
    at Array.map (<anonymous>)
    at pve.Mb (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1750:33664)
    at vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1750:33571
2025-06-13 20:45:15.961 [error] [Extension Host] Error during login process: ConnectError: [permission_denied] api server wire error: free user account exceeded, please use an existing account or upgrade to a paid plan
	at c (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856441)
	at l (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856837)
	at next (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3863488)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3462828
	at async Object.unary (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3861824)
	at async Object.getUserStatus (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3849687)
	at async v (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2282983)
	at async t.initializeAuthSession (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2282205)
