2025-06-13 21:34:15.856 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.856 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.856 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.856 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.856 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.856 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.857 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.857 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.857 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.857 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:34:15.953 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-06-13 21:34:15.960 [warning] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-06-13 21:34:16.004 [error] Cannot read properties of undefined (reading 'label'): TypeError: Cannot read properties of undefined (reading 'label')
    at _ce.eb (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:844:11629)
    at _ce.db (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:844:9734)
    at _ce.ab (vscode-file://vscode-app/Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:844:8805)
2025-06-13 21:34:16.117 [error] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-06-13 21:34:16.131 [warning] [ms-python.python]: Cannot register 'python.venvPath'. This property is already registered.
2025-06-13 21:34:16.475 [info] Started local extension host with pid 11948.
2025-06-13 21:34:20.189 [info] [perf] Render performance baseline is 29ms
2025-06-13 21:37:46.169 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.170 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.170 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:37:46.225 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-06-13 21:37:46.234 [warning] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-06-13 21:37:46.307 [info] Started local extension host with pid 13016.
2025-06-13 21:37:46.473 [error] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-06-13 21:37:48.871 [warning] [Codeium.windsurfPyright]: Cannot register 'python.venvPath'. This property is already registered.
2025-06-13 21:37:49.693 [info] [perf] Render performance baseline is 30ms
2025-06-13 21:37:53.705 [error] [Extension Host] Failed to get remote url: Error: Failed to get remote url, no remote found
	at lU.handleGetRemoteUrlRequest (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1928:492)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1596:3962
	at async e.runTimed (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2210:84893)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1596:3920
2025-06-13 21:37:55.916 [error] An unknown error occurred. Please consult the log for more details.
