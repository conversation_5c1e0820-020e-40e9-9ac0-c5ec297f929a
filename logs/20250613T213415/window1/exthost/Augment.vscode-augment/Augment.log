2025-06-13 21:34:24.756 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 21:34:24.756 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"用中文回答"},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-13 21:34:24.756 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-13 21:34:28.650 [info] 'OAuthFlow' Creating new session...
2025-06-13 21:34:28.654 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=XwNtDXJpNfONZXRngd1yAhd7BYM4s08gYjf9QE0gJmw&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=windsurf%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=28298f4f-a2f5-431b-b7cb-d434fbefb83b&scope=email&prompt=login
2025-06-13 21:37:20.141 [info] 'activate()' ======== Reloading extension ========
2025-06-13 21:37:20.144 [info] 'AugmentExtension' Retrieving model config
2025-06-13 21:37:20.145 [info] 'OAuthFlow' Created session https://d2.api.augmentcode.com/
2025-06-13 21:37:22.456 [info] 'AugmentExtension' Retrieved model config
2025-06-13 21:37:22.456 [info] 'AugmentExtension' Returning model config
2025-06-13 21:37:22.479 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-13 21:37:22.479 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-06-13 21:37:22.479 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [false]
2025-06-13 21:37:22.479 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-13 21:37:22.532 [info] 'WorkspaceManager' Workspace startup complete in 15 ms
2025-06-13 21:37:22.532 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 21:37:22.532 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 21:37:22.532 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-13 21:37:22.533 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
2025-06-13 21:37:25.430 [error] 'AugmentExtension' API request 17a9883b-fa72-43e1-a4a8-ca62a6823788 to https://d2.api.augmentcode.com/agents/list-remote-tools response 403: Forbidden
2025-06-13 21:37:25.944 [error] 'AugmentExtension' API request 3773517e-51aa-4ee6-8d03-e627338bbef4 to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:37:25.944 [error] 'AugmentExtension' Dropping error report "agents/list-remote-tools call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:37:25.944 [error] 'VSCodeRemoteInfo' Failed to list remote tools HTTP error: 403 Forbidden
2025-06-13 21:37:25.945 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
2025-06-13 21:37:33.585 [error] 'AugmentExtension' API request b751288e-a06f-43b9-8898-6817efc2659c to https://d2.api.augmentcode.com/report-feature-vector response 403: Forbidden
2025-06-13 21:37:33.597 [error] 'AugmentExtension' API request fc6713a0-4244-4cea-b7de-7d8715193d44 to https://d2.api.augmentcode.com/record-session-events response 403: Forbidden
2025-06-13 21:37:33.744 [error] 'AugmentExtension' API request 5686f7ba-d023-49cf-91a1-24dd30822dac to https://d2.api.augmentcode.com/record-session-events response 403: Forbidden
2025-06-13 21:37:34.094 [error] 'AugmentExtension' API request 04ae5573-7456-46fa-8a18-a5d13a665813 to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:37:34.094 [error] 'AugmentExtension' Dropping error report "report-feature-vector call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:37:34.094 [error] 'FeatureVectorReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logFeatureVector (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:33760)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:37:34.094 [error] 'FeatureVectorReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logFeatureVector (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:33760)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:37:34.115 [error] 'AugmentExtension' API request 51a9c498-b262-4445-949b-69e98baefbb3 to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:37:34.116 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:37:34.116 [error] 'OnboardingSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logOnboardingSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:34327)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:37:34.116 [error] 'OnboardingSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logOnboardingSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:34327)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:37:34.122 [error] 'AugmentExtension' API request c60b8fd7-fe34-49b3-9672-d5bb0f0df743 to https://d2.api.augmentcode.com/record-session-events response 403: Forbidden
2025-06-13 21:37:34.249 [error] 'AugmentExtension' API request fc7e850d-8add-4b39-8bbf-b1b7295bc83f to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:37:34.249 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:37:34.249 [error] 'ExtensionSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logExtensionSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:35799)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:37:34.249 [error] 'ExtensionSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logExtensionSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:35799)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:37:34.625 [error] 'AugmentExtension' API request c75f4525-4553-4434-9366-f0de1a2c1447 to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:37:34.625 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:37:34.625 [error] 'NextEditSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logNextEditSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:33964)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:37:34.625 [error] 'NextEditSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logNextEditSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:33964)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:37:49.104 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 21:37:49.104 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"用中文回答"},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-13 21:37:49.104 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-13 21:37:49.104 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 21:37:49.104 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 21:37:49.104 [info] 'AugmentExtension' Retrieving model config
2025-06-13 21:37:51.332 [info] 'AugmentExtension' Retrieved model config
2025-06-13 21:37:51.332 [info] 'AugmentExtension' Returning model config
2025-06-13 21:37:51.344 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-13 21:37:51.344 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-06-13 21:37:51.344 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [false]
2025-06-13 21:37:51.344 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-13 21:37:51.344 [info] 'SyncingPermissionTracker' Permission to sync folder /Users/<USER>/Library/Application Support/Windsurf unknown: no permission information recorded
2025-06-13 21:37:51.344 [info] 'WorkspaceManager' Adding workspace folder Windsurf; folderRoot = /Users/<USER>/Library/Application Support/Windsurf; syncingPermission = unknown
2025-06-13 21:37:51.389 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 21:37:51.389 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 21:37:51.389 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-13 21:37:51.390 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
2025-06-13 21:37:51.427 [info] 'WorkspaceManager' Beginning full qualification of source folder /Users/<USER>/Library/Application Support/Windsurf
2025-06-13 21:37:52.240 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 21:37:53.598 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-13 21:37:53.598 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-13 21:37:53.599 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-13 21:37:53.599 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 21:37:53.612 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 21:37:53.613 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 21:37:53.614 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 21:37:53.705 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-13 21:37:53.715 [info] 'TaskManager' Setting current root task UUID to a9791a89-09ae-4ab0-ba6c-a4e4cc411886
2025-06-13 21:37:53.716 [info] 'TaskManager' Setting current root task UUID to a9791a89-09ae-4ab0-ba6c-a4e4cc411886
2025-06-13 21:37:53.717 [info] 'TaskManager' Setting current root task UUID to 0abd1f27-d72c-4c22-8aa4-f01d236aaead
2025-06-13 21:37:53.718 [info] 'TaskManager' Setting current root task UUID to 0abd1f27-d72c-4c22-8aa4-f01d236aaead
2025-06-13 21:37:53.723 [info] 'TaskManager' Setting current root task UUID to 32a9a376-e717-4a91-bd64-41e0408e141d
2025-06-13 21:37:53.724 [info] 'TaskManager' Setting current root task UUID to 32a9a376-e717-4a91-bd64-41e0408e141d
2025-06-13 21:37:53.724 [info] 'TaskManager' Setting current root task UUID to 198405ba-475d-4e90-afb2-e8fe7713fd15
2025-06-13 21:37:53.724 [info] 'TaskManager' Setting current root task UUID to 198405ba-475d-4e90-afb2-e8fe7713fd15
2025-06-13 21:37:53.812 [error] 'AugmentExtension' API request a0267e02-409c-4acb-a4bd-3ddad82e13f5 to https://d2.api.augmentcode.com/agents/list-remote-tools response 403: Forbidden
2025-06-13 21:37:54.245 [error] 'AugmentExtension' API request 3ce44f35-176c-4da4-b9f8-1efc98aa74d5 to https://d2.api.augmentcode.com/find-missing response 403: Forbidden
2025-06-13 21:37:54.314 [error] 'AugmentExtension' API request c54ab395-a8c5-4b4f-a1d6-73a4fea96e74 to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:37:54.314 [error] 'AugmentExtension' Dropping error report "agents/list-remote-tools call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:37:54.314 [error] 'VSCodeRemoteInfo' Failed to list remote tools HTTP error: 403 Forbidden
2025-06-13 21:37:54.314 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 21:37:54.907 [error] 'AugmentExtension' API request 74f320c2-1494-4550-afb6-92cb75c0587e to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:37:54.907 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:37:54.933 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1261.611042,"timestamp":"2025-06-13T13:37:54.862Z"}]
2025-06-13 21:37:56.161 [info] 'StallDetector' Recent work: [{"name":"get-remote-agent-overviews-request","durationMs":2531.750667,"timestamp":"2025-06-13T13:37:56.144Z"}]
2025-06-13 21:37:58.714 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-ad0f9cf9-1d12-435d-b2b0-39be3d51c664.json'
2025-06-13 21:38:02.620 [error] 'AugmentExtension' API request 51353297-c186-4b35-a69d-ff8fe3227d3a to https://d2.api.augmentcode.com/report-feature-vector response 403: Forbidden
2025-06-13 21:38:02.956 [error] 'AugmentExtension' API request 5220134b-ae28-48d9-9c33-c81b5062f802 to https://d2.api.augmentcode.com/record-session-events response 403: Forbidden
2025-06-13 21:38:03.123 [error] 'AugmentExtension' API request aa7e2f6b-4b69-43f1-8649-13ba68cce9c1 to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:38:03.123 [error] 'AugmentExtension' Dropping error report "report-feature-vector call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:38:03.123 [error] 'FeatureVectorReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logFeatureVector (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:33760)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:38:03.123 [error] 'FeatureVectorReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logFeatureVector (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:33760)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:38:03.461 [error] 'AugmentExtension' API request 1d627805-c317-4a93-b7b2-5f0a7aeecc8e to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:38:03.462 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:38:03.462 [error] 'OnboardingSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logOnboardingSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:34327)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:38:03.462 [error] 'OnboardingSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logOnboardingSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:34327)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:38:03.545 [error] 'AugmentExtension' API request e2b819d2-9921-4687-b429-e37791816a4f to https://d2.api.augmentcode.com/record-session-events response 403: Forbidden
2025-06-13 21:38:03.691 [error] 'AugmentExtension' API request 8856a12c-8514-4340-9fc6-013f544d0364 to https://d2.api.augmentcode.com/record-session-events response 403: Forbidden
2025-06-13 21:38:04.052 [error] 'AugmentExtension' API request c876729f-d8e8-43e1-b46e-bddf1084d8c4 to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:38:04.052 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:38:04.052 [error] 'NextEditSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logNextEditSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:33964)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:38:04.052 [error] 'NextEditSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logNextEditSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:33964)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:38:04.199 [error] 'AugmentExtension' API request 7ca1a354-5f17-4ae4-b7df-85b06a7d75e0 to https://d2.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-13 21:38:04.199 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-06-13 21:38:04.199 [error] 'ExtensionSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logExtensionSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:35799)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:38:04.199 [error] 'ExtensionSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
	at e.fromResponse (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:10200)
	at kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:13525)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async kQ.callApi (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:56371)
	at async kQ.logExtensionSessionEvent (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1581:35799)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14361
	at async Is (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:12573)
	at async e._doUpload (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:14260)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:665:13583
2025-06-13 21:38:20.539 [warning] 'OAuthFlow' Failed to process auth request: No OAuth state found
2025-06-13 21:38:24.738 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1202.801792,"timestamp":"2025-06-13T13:38:24.738Z"}]
2025-06-13 21:38:56.063 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1793.084541,"timestamp":"2025-06-13T13:38:55.978Z"}]
