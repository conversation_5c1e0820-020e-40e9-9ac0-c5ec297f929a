2025-06-13 21:33:24.497 [info] Extension host with pid 10277 started
2025-06-13 21:33:24.504 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-13 21:33:24.521 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-13 21:33:24.522 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-13 21:33:24.546 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-13 21:33:24.602 [info] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-06-13 21:33:32.507 [info] Eager extensions activated
2025-06-13 21:33:32.512 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:33:32.514 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:33:32.525 [info] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:33:32.528 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:33:32.673 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:34:04.273 [info] Extension host terminating: renderer closed the MessagePort
2025-06-13 21:34:04.284 [info] Extension host with pid 10277 exiting with code 0
