2025-06-13 21:47:06.905 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:06.906 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-13 21:47:07.014 [warning] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-06-13 21:47:07.119 [info] Started local extension host with pid 15523.
2025-06-13 21:47:07.345 [error] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-06-13 21:47:07.362 [warning] [ms-python.python]: Cannot register 'python.venvPath'. This property is already registered.
2025-06-13 21:47:09.835 [info] [perf] Render performance baseline is 30ms
2025-06-13 21:47:20.772 [error] [Extension Host] Failed to get remote url: Error: Failed to get remote url, no remote found
	at lU.handleGetRemoteUrlRequest (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1928:492)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1596:3962
	at async e.runTimed (/Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:2210:84893)
	at async /Users/<USER>/.windsurf/extensions/augment.vscode-augment-0.477.2/out/extension.js:1596:3920
