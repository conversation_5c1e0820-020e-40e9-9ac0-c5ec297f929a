2025-06-13 21:47:07.705 [info] [main] Log level: Info
2025-06-13 21:47:07.712 [info] [main] Validating found git in: "/usr/bin/git"
2025-06-13 21:47:07.739 [info] [main] Using git "2.39.3 (Apple Git-146)" from "/usr/bin/git"
2025-06-13 21:47:07.739 [info] [Model][doInitialScan] Initial repository scan started
2025-06-13 21:47:07.991 [info] > git rev-parse --show-toplevel [23ms]
2025-06-13 21:47:07.991 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.005 [info] > git rev-parse --show-toplevel [13ms]
2025-06-13 21:47:08.005 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.024 [info] > git rev-parse --show-toplevel [16ms]
2025-06-13 21:47:08.024 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.040 [info] > git rev-parse --show-toplevel [15ms]
2025-06-13 21:47:08.040 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.053 [info] > git rev-parse --show-toplevel [12ms]
2025-06-13 21:47:08.053 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.061 [info] > git rev-parse --show-toplevel [7ms]
2025-06-13 21:47:08.061 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.070 [info] > git rev-parse --show-toplevel [8ms]
2025-06-13 21:47:08.070 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.078 [info] > git rev-parse --show-toplevel [6ms]
2025-06-13 21:47:08.078 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.086 [info] > git rev-parse --show-toplevel [8ms]
2025-06-13 21:47:08.087 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.094 [info] > git rev-parse --show-toplevel [7ms]
2025-06-13 21:47:08.094 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.104 [info] > git rev-parse --show-toplevel [9ms]
2025-06-13 21:47:08.104 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.113 [info] > git rev-parse --show-toplevel [7ms]
2025-06-13 21:47:08.113 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.122 [info] > git rev-parse --show-toplevel [9ms]
2025-06-13 21:47:08.122 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.129 [info] > git rev-parse --show-toplevel [6ms]
2025-06-13 21:47:08.129 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.137 [info] > git rev-parse --show-toplevel [7ms]
2025-06-13 21:47:08.137 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.143 [info] > git rev-parse --show-toplevel [6ms]
2025-06-13 21:47:08.144 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.151 [info] > git rev-parse --show-toplevel [7ms]
2025-06-13 21:47:08.151 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.158 [info] > git rev-parse --show-toplevel [6ms]
2025-06-13 21:47:08.158 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.166 [info] > git rev-parse --show-toplevel [7ms]
2025-06-13 21:47:08.166 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:08.168 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-13 21:47:09.614 [info] > git rev-parse --show-toplevel [8ms]
2025-06-13 21:47:09.614 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-13 21:47:09.947 [info] > git rev-parse --show-toplevel [13ms]
2025-06-13 21:47:09.947 [info] fatal: not a git repository (or any of the parent directories): .git
