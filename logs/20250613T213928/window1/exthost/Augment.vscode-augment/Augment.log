2025-06-13 21:39:36.137 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 21:39:36.138 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"用中文回答"},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-13 21:39:36.138 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-13 21:39:36.138 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 21:39:53.523 [info] 'OAuthFlow' Creating new session...
2025-06-13 21:39:53.526 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=Ns84whYqSKtwxCXPKg_D7WbIw25XVDHlYz0_vK9_550&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=windsurf%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=5463beab-5d3d-4d63-8e84-bcf311fea7d0&scope=email&prompt=login
2025-06-13 21:41:00.366 [info] 'OAuthFlow' Creating new session...
2025-06-13 21:41:00.368 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=J0gI7KEtWSYtQbpZUkC6_N81bgBYKecdiN5fb5LXENk&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=windsurf%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=6cef3193-3db8-4c9c-94eb-cb3aef5f173e&scope=email&prompt=login
2025-06-13 21:42:55.297 [info] 'OAuthFlow' Creating new session...
2025-06-13 21:42:55.300 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=__qXbNuXHDUOHphyDkajzbylv7-GvP4d9_FMPQFcrJM&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=windsurf%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=26341b70-69ca-4f2c-9655-580696d95810&scope=email&prompt=login
2025-06-13 21:44:28.765 [warning] 'OAuthFlow' Failed to process auth request: No OAuth state found
2025-06-13 21:44:31.928 [info] 'OAuthFlow' Creating new session...
2025-06-13 21:44:31.931 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=I0dGtRpW3Zv0esZ7rUcHc94zC9b7xIdhHm3qFaEe_yE&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=windsurf%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=c19afae3-0e38-456f-a1a4-fe076f974c3c&scope=email&prompt=login
2025-06-13 21:45:45.056 [info] 'OAuthFlow' Creating new session...
2025-06-13 21:45:45.057 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=7aj95HZGs-mBfPzo7vidbL2_g0gWxAVOhnolrnaqE-Q&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=windsurf%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=749860f2-45ff-4062-a088-661713bb9a6f&scope=email&prompt=login
2025-06-13 21:46:11.276 [warning] 'OAuthFlow' Failed to process auth request: No OAuth state found
2025-06-13 21:46:15.840 [info] 'OAuthFlow' Creating new session...
2025-06-13 21:46:15.843 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=iqxBzGzbcESKDuoqhr69blHZlp53kWRCr5K6ABhc7_M&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=windsurf%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=2c54618e-4bd3-4f5a-97e2-88a374b3daa3&scope=email&prompt=login
2025-06-13 21:46:50.801 [info] 'activate()' ======== Reloading extension ========
2025-06-13 21:46:50.801 [info] 'AugmentExtension' Retrieving model config
2025-06-13 21:46:50.802 [info] 'OAuthFlow' Created session https://i1.api.augmentcode.com/
2025-06-13 21:46:52.268 [info] 'AugmentExtension' Retrieved model config
2025-06-13 21:46:52.268 [info] 'AugmentExtension' Returning model config
2025-06-13 21:46:52.296 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-13 21:46:52.296 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-06-13 21:46:52.296 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-13 21:46:52.296 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-13 21:46:52.296 [info] 'SyncingPermissionTracker' Permission to sync folder /Users/<USER>/Library/Application Support/Windsurf unknown: no permission information recorded
2025-06-13 21:46:52.296 [info] 'WorkspaceManager' Adding workspace folder Windsurf; folderRoot = /Users/<USER>/Library/Application Support/Windsurf; syncingPermission = unknown
2025-06-13 21:46:52.348 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 21:46:52.348 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 21:46:52.348 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-13 21:46:52.349 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
2025-06-13 21:46:52.365 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-13 21:46:52.365 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-13 21:46:52.368 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-13 21:46:52.368 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 21:46:52.369 [info] 'TaskManager' Setting current root task UUID to 198405ba-475d-4e90-afb2-e8fe7713fd15
2025-06-13 21:46:52.455 [info] 'WorkspaceManager' Beginning full qualification of source folder /Users/<USER>/Library/Application Support/Windsurf
2025-06-13 21:46:52.536 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-13 21:46:53.610 [info] 'StallDetector' Recent work: [{"name":"get-remote-agent-overviews-request","durationMs":1175.77475,"timestamp":"2025-06-13T13:46:53.548Z"}]
2025-06-13 21:46:53.712 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1339.232209,"timestamp":"2025-06-13T13:46:53.708Z"}]
2025-06-13 21:46:53.992 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 21:46:55.347 [info] 'WorkspaceManager' Finished full qualification of source folder /Users/<USER>/Library/Application Support/Windsurf: trackable files: 720, uploaded fraction: 0.04332129963898917, is repo: false
2025-06-13 21:46:55.348 [info] 'WorkspaceManager' Requesting syncing permission because source folder does not appear to be a source repo
2025-06-13 21:46:55.349 [info] 'AwaitingSyncingPermissionApp' Registering AwaitingSyncingPermissionApp
2025-06-13 21:47:09.255 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 21:47:09.255 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"用中文回答"},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-13 21:47:09.255 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-13 21:47:09.255 [info] 'AugmentExtension' Retrieving model config
2025-06-13 21:47:10.609 [info] 'AugmentExtension' Retrieved model config
2025-06-13 21:47:10.609 [info] 'AugmentExtension' Returning model config
2025-06-13 21:47:10.620 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-13 21:47:10.620 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-06-13 21:47:10.620 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-13 21:47:10.620 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-13 21:47:10.673 [info] 'WorkspaceManager' Workspace startup complete in 7 ms
2025-06-13 21:47:10.673 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 21:47:10.673 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 21:47:10.673 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-13 21:47:10.675 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
