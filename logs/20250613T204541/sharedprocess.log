2025-06-13 20:45:42.597 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async Promise.all (index 0)
    at async da.r (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:40624)
    at async qu.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33645)
    at async vh.I (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:73378)
    at async vh.cleanUp (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:66881)
    at async wh.cleanUp (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:61234)
2025-06-13 20:45:42.648 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async Promise.all (index 0)
    at async da.r (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:40624)
    at async qu.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33645)
    at async vh.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67437)
    at async U2.H (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:57403)
    at async U2.s (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:55298)
2025-06-13 20:45:46.907 [info] Getting Manifest... codeium.windsurfpyright
2025-06-13 20:45:47.266 [info] Getting Manifest... ms-dotnettools.vscode-dotnet-runtime
2025-06-13 20:45:49.640 [info] Installing extension: ms-dotnettools.vscode-dotnet-runtime {"productVersion":{"version":"1.99.3","date":"2025-06-12T07:31:38.222Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json","path":"/Users/<USER>/.windsurf/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-06-13 20:45:49.652 [info] Installing extension: codeium.windsurfpyright {"isMachineScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json","path":"/Users/<USER>/.windsurf/extensions/extensions.json","scheme":"vscode-userdata"},"isApplicationScoped":false,"productVersion":{"version":"1.99.3","date":"2025-06-12T07:31:38.222Z"}}
2025-06-13 20:45:50.100 [info] Getting Manifest... ms-python.python
2025-06-13 20:45:51.430 [info] Getting Manifest... ms-python.debugpy
2025-06-13 20:45:52.217 [info] Extracted extension to file:///Users/<USER>/.windsurf/extensions/ms-dotnettools.vscode-dotnet-runtime-2.3.6-universal: ms-dotnettools.vscode-dotnet-runtime
2025-06-13 20:45:52.223 [info] Renamed to /Users/<USER>/.windsurf/extensions/ms-dotnettools.vscode-dotnet-runtime-2.3.6-universal
2025-06-13 20:45:52.235 [info] Marked extension as removed ms-dotnettools.vscode-dotnet-runtime-2.2.8
2025-06-13 20:45:52.239 [info] Extension installed successfully: ms-dotnettools.vscode-dotnet-runtime vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
2025-06-13 20:45:55.729 [info] Extracted extension to file:///Users/<USER>/.windsurf/extensions/codeium.windsurfpyright-1.28.0-universal: codeium.windsurfpyright
2025-06-13 20:45:55.824 [info] Renamed to /Users/<USER>/.windsurf/extensions/codeium.windsurfpyright-1.28.0-universal
2025-06-13 20:45:55.839 [info] Extension installed successfully: codeium.windsurfpyright vscode-userdata:/Users/<USER>/.windsurf/extensions/extensions.json
