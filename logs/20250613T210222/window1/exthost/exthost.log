2025-06-13 21:02:24.405 [info] Extension host with pid 1609 started
2025-06-13 21:02:24.412 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-13 21:02:24.429 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-13 21:02:24.431 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-13 21:02:24.455 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-13 21:02:24.512 [info] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-06-13 21:02:29.763 [info] Eager extensions activated
2025-06-13 21:02:29.768 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:02:29.770 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:02:29.781 [info] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:02:29.784 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:02:29.939 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 21:03:09.245 [error] ConnectError: [permission_denied] api server wire error: free user account exceeded, please use an existing account or upgrade to a paid plan
	at c (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856441)
	at l (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856837)
	at next (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3863488)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3462828
	at async Object.unary (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3861824)
	at async Object.getUserStatus (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3849687)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2477188
	at async CI.h (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:41496)
	at async L.handleAsyncPostMessage (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2494350)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3897776
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3898046
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3897691 windsurf.updatePlanInfo {"value":"codeium.windsurf","_lower":"codeium.windsurf"}
2025-06-13 21:03:13.675 [error] ConnectError: [permission_denied] api server wire error: free user account exceeded, please use an existing account or upgrade to a paid plan
	at c (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856441)
	at l (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3856837)
	at next (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3863488)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3462828
	at async Object.unary (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3861824)
	at async Object.getUserStatus (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3849687)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2477188
	at async CI.h (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:41496)
	at async L.handleAsyncPostMessage (/Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:2494350)
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3897776
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3898046
	at async /Applications/Windsurf.app/Contents/Resources/app/extensions/windsurf/dist/extension.js:2:3897691 windsurf.updatePlanInfo {"value":"codeium.windsurf","_lower":"codeium.windsurf"}
2025-06-13 21:03:18.447 [info] Extension host terminating: renderer closed the MessagePort
2025-06-13 21:03:18.457 [info] Extension host with pid 1609 exiting with code 0
