2025-06-13 21:02:24.221 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async Promise.all (index 0)
    at async da.r (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:40624)
    at async qu.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33645)
    at async vh.I (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:73378)
    at async vh.cleanUp (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:66881)
    at async wh.cleanUp (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:61234)
2025-06-13 21:02:24.224 [error] Error: Unable to read file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json' (Error: Unable to resolve nonexistent file '/Users/<USER>/.windsurf/extensions/anseki.vscode-color-0.4.5/package.json')
    at Eh.G (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86829)
    at Eh.F (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86654)
    at async Eh.D (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:86026)
    at async da.u (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:43691)
    at async da.scanExtension (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:42198)
    at async Promise.all (index 0)
    at async da.r (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:40624)
    at async qu.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:33645)
    at async vh.scanAllUserExtensions (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:67437)
    at async U2.H (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:57403)
    at async U2.s (file:///Applications/Windsurf.app/Contents/Resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:55298)
