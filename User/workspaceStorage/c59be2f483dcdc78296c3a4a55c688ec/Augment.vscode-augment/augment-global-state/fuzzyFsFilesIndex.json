{"/Users/<USER>/Library/Application Support/Windsurf/WebStorage/QuotaManager-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/QuotaManager-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/7f48cd86-72e9-418a-b24d-2c056f5e97ab": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/7f48cd86-72e9-418a-b24d-2c056f5e97ab"}, "/Users/<USER>/Library/Application Support/Windsurf/Cookies-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cookies-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/SharedStorage": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "SharedStorage"}, "/Users/<USER>/Library/Application Support/Windsurf/SharedStorage-wal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "SharedStorage-wal"}, "/Users/<USER>/Library/Application Support/Windsurf/Trust Tokens-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Trust Tokens-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/languagepacks.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "languagepacks.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Shared Dictionary/db-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Shared Dictionary/db-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/storage.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/storage.json"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/3/CacheStorage/5cabeec0-710a-42de-b401-1946f083ab96/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/3/CacheStorage/5cabeec0-710a-42de-b401-1946f083ab96/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/3/CacheStorage/index.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/3/CacheStorage/index.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/836a8e9a6ccc2c28_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/836a8e9a6ccc2c28_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/9ec96f4f02f74c83_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/9ec96f4f02f74c83_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/aa458a504226549c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/aa458a504226549c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/f8b50749833d79e7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/f8b50749833d79e7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/vscode.json-language-features/JSON Language Server.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/Network Persistent State": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Network Persistent State"}, "/Users/<USER>/Library/Application Support/Windsurf/Preferences": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Preferences"}, "/Users/<USER>/Library/Application Support/Windsurf/TransportSecurity": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "TransportSecurity"}, "/Users/<USER>/Library/Application Support/Windsurf/code.lock": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "code.lock"}, "/Users/<USER>/Library/Application Support/Windsurf/machineid": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "machineid"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/output_20250613T214707/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/output_20250613T214707/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/output_logging_20250613T214707/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/output_logging_20250613T214707/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/output_logging_20250613T214707/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/output_logging_20250613T214707/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/output_20250613T214703/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/output_20250613T214703/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/output_20250613T213928/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/output_20250613T213928/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/output_logging_20250613T214703/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/output_logging_20250613T214703/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/output_logging_20250613T214703/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/output_logging_20250613T214703/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/output_logging_20250613T213929/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/output_logging_20250613T213929/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/output_logging_20250613T213929/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/output_logging_20250613T213929/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749821978689.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749821978689.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749822429222.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749822429222.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/output_20250613T213746/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/output_20250613T213746/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/output_20250613T213416/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/output_20250613T213416/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/vscode.json-language-features/JSON Language Server.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/output_logging_20250613T213746/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/output_logging_20250613T213746/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/output_logging_20250613T213746/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/output_logging_20250613T213746/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/output_logging_20250613T213416/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/output_logging_20250613T213416/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/output_logging_20250613T213416/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/output_logging_20250613T213416/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749821664729.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749821664729.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749821873601.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749821873601.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/output_20250613T213323/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/output_20250613T213323/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/output_logging_20250613T213324/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/output_logging_20250613T213324/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/output_logging_20250613T213324/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/output_logging_20250613T213324/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749821612735.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749821612735.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/output_20250613T212108/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/output_20250613T212108/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/output_logging_20250613T212109/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/output_logging_20250613T212109/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/output_logging_20250613T212109/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/output_logging_20250613T212109/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749820874880.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749820874880.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/output_20250613T210500/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/output_20250613T210500/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/output_logging_20250613T210500/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/output_logging_20250613T210500/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/output_logging_20250613T210500/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/output_logging_20250613T210500/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749819908497.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749819908497.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/output_20250613T210326/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/output_20250613T210326/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/output_logging_20250613T210327/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/output_logging_20250613T210327/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/output_logging_20250613T210327/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/output_logging_20250613T210327/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749819811759.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749819811759.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/output_20250613T210223/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/output_20250613T210223/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/output_logging_20250613T210224/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/output_logging_20250613T210224/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/output_logging_20250613T210224/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/output_logging_20250613T210224/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749819750005.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749819750005.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/output_20250613T204732/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/output_20250613T204732/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/output_logging_20250613T204733/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/output_logging_20250613T204733/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/output_logging_20250613T204733/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/output_logging_20250613T204733/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749818858364.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749818858364.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/output_20250613T204542/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/output_20250613T204542/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/output_logging_20250613T204542/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/output_logging_20250613T204542/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/output_logging_20250613T204542/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/output_logging_20250613T204542/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749818747431.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749818747431.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/output_20250613T204354/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/output_20250613T204354/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/webWorker/workerExtHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/webWorker/workerExtHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/webWorker/workerexthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/webWorker/workerexthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/vscode.json-language-features/JSON Language Server.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/output_logging_20250613T204507/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/output_logging_20250613T204507/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/output_logging_20250613T204507/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/output_logging_20250613T204507/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/output_logging_20250613T204354/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/output_logging_20250613T204354/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749818711123.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749818711123.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/User/keybindings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/keybindings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/workspace.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/workspace.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/Augment-Memories": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/Augment-Memories"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/0abd1f27-d72c-4c22-8aa4-f01d236aaead": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/0abd1f27-d72c-4c22-8aa4-f01d236aaead"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/198405ba-475d-4e90-afb2-e8fe7713fd15": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/198405ba-475d-4e90-afb2-e8fe7713fd15"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/32a9a376-e717-4a91-bd64-41e0408e141d": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/32a9a376-e717-4a91-bd64-41e0408e141d"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/a9791a89-09ae-4ab0-ba6c-a4e4cc411886": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/a9791a89-09ae-4ab0-ba6c-a4e4cc411886"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/44f5d739/dRQ9.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/44f5d739/dRQ9.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/44f5d739/entries.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/44f5d739/entries.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/42cc0f23/QbjR.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/42cc0f23/QbjR.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/42cc0f23/entries.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/42cc0f23/entries.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedProfilesData/__default__profile__/extensions.user.cache": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedProfilesData/__default__profile__/extensions.user.cache"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/653e56b4b6556a9e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/653e56b4b6556a9e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/653e56b4b6556a9e_1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/653e56b4b6556a9e_1"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/a3ea6efa51cedf2c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/a3ea6efa51cedf2c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/f76ccbbbd72a0391_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/f76ccbbbd72a0391_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/f76ccbbbd72a0391_1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/f76ccbbbd72a0391_1"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/09f7f5155b158bb0_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/09f7f5155b158bb0_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/127582112d54dacd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/127582112d54dacd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/450354df6aee8093_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/450354df6aee8093_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/5c0451da7bb4ee15_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/5c0451da7bb4ee15_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/6db0a6babba51a67_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/6db0a6babba51a67_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/79097449ae97b6f2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/79097449ae97b6f2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/7c3f868ec972e80a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/7c3f868ec972e80a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/83321c8c81976fd1_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/83321c8c81976fd1_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/bae0ceaa3a95ab53_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/bae0ceaa3a95ab53_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/c3b0fe0395597456_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/c3b0fe0395597456_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/c4f0972cfc39d807_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/c4f0972cfc39d807_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/cb0047afd649c15e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/cb0047afd649c15e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/e83dceae07726a32_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/e83dceae07726a32_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/index"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/index.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/index.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/6ba72320325dda77_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/6ba72320325dda77_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/d4e72de5f8589631_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/d4e72de5f8589631_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/067c42b282ed5bc3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/067c42b282ed5bc3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/096999c976fe1caa_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/096999c976fe1caa_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/162a1ac5c458f8f6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/162a1ac5c458f8f6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/18866b026410dcb2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/18866b026410dcb2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/1ae08574d7b5b16b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/1ae08574d7b5b16b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/1f89246b65936bb6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/1f89246b65936bb6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/26fe29b2d6edabbd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/26fe29b2d6edabbd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/2c52aa28301a47ae_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/2c52aa28301a47ae_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/31aa2f923b6dd7f9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/31aa2f923b6dd7f9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/38acc09fa83b1213_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/38acc09fa83b1213_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/4c554dc0edff53d2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/4c554dc0edff53d2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/676a5e1f9b92da56_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/676a5e1f9b92da56_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/6de034b06377b42c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/6de034b06377b42c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/75d88cdb985f286a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/75d88cdb985f286a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/81864df89e837fdb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/81864df89e837fdb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/8b9f1678677e6c46_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/8b9f1678677e6c46_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/91ef31671bd2b401_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/91ef31671bd2b401_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/952e0e0aa47e6a8a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/952e0e0aa47e6a8a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/95eaaca353b9940f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/95eaaca353b9940f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/9871f11884a56240_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/9871f11884a56240_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/b152ade71171e5a8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/b152ade71171e5a8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/c84b024db759a884_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/c84b024db759a884_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/cce75b58899c6471_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/cce75b58899c6471_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/dc3a26b133297994_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/dc3a26b133297994_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/de8e0ee06a22181e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/de8e0ee06a22181e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/e784111b6b971887_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/e784111b6b971887_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/efa6818a76e5fa75_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/4/CacheStorage/d76cfe03-b864-4eb8-a3cc-ec690602f873/efa6818a76e5fa75_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/23469ac906fcc612_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/23469ac906fcc612_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/23469ac906fcc612_1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/23469ac906fcc612_1"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/6d8cffc40708e610_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/6d8cffc40708e610_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/1c3ede7ca78d7c9d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/1c3ede7ca78d7c9d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/7cc3be84233829c8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/7cc3be84233829c8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/459ce233-9bf9-4147-bc20-d1d4def07f28": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/459ce233-9bf9-4147-bc20-d1d4def07f28"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/35b6b25ef8200536_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/35b6b25ef8200536_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/3/CacheStorage/5cabeec0-710a-42de-b401-1946f083ab96/a4c6aac8b18c180d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/3/CacheStorage/5cabeec0-710a-42de-b401-1946f083ab96/a4c6aac8b18c180d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/5d7323f3dd92e822_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/5d7323f3dd92e822_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/a4f662b7f9be287b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/a4f662b7f9be287b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/3/CacheStorage/5cabeec0-710a-42de-b401-1946f083ab96/d68e9643435d34f3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/3/CacheStorage/5cabeec0-710a-42de-b401-1946f083ab96/d68e9643435d34f3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/b48837b9df4717fb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/b48837b9df4717fb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/06d568c897c8def2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/06d568c897c8def2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ad1a1a8d75753160_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ad1a1a8d75753160_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7eacac2db1205ad6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7eacac2db1205ad6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/7b221ea803dc3a8f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/7b221ea803dc3a8f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/bb81eee35b78c7ad_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/bb81eee35b78c7ad_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4e42da54b0b0929c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4e42da54b0b0929c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/2a7876a1-a422-4b38-b8c0-75ffdef49b1c": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/2a7876a1-a422-4b38-b8c0-75ffdef49b1c"}}