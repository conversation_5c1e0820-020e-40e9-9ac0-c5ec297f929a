{"/Users/<USER>/Library/Application Support/Windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": ""}, "/Users/<USER>/Library/Application Support/Windsurf/Shared Dictionary/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Shared Dictionary/"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/output_20250613T214707/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/output_20250613T214707/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/output_logging_20250613T214707/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/output_logging_20250613T214707/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/output_20250613T214703/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/output_20250613T214703/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/output_20250613T213928/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/output_20250613T213928/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/output_logging_20250613T214703/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/output_logging_20250613T214703/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/output_logging_20250613T213929/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/output_logging_20250613T213929/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/output_20250613T213746/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/output_20250613T213746/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/output_20250613T213416/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/output_20250613T213416/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/vscode.json-language-features/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/vscode.json-language-features/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/output_logging_20250613T213746/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/output_logging_20250613T213746/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/output_logging_20250613T213416/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/output_logging_20250613T213416/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213415/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213415/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/output_20250613T213323/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/output_20250613T213323/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/output_logging_20250613T213324/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/output_logging_20250613T213324/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213322/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213322/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/output_20250613T212108/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/output_20250613T212108/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/output_logging_20250613T212109/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/output_logging_20250613T212109/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T212107/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T212107/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/output_20250613T210500/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/output_20250613T210500/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/output_logging_20250613T210500/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/output_logging_20250613T210500/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210459/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210459/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/output_20250613T210326/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/output_20250613T210326/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/output_logging_20250613T210327/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/output_logging_20250613T210327/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210325/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210325/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/output_20250613T210223/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/output_20250613T210223/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/output_logging_20250613T210224/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/output_logging_20250613T210224/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T210222/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T210222/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/output_20250613T204732/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/output_20250613T204732/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/output_logging_20250613T204733/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/output_logging_20250613T204733/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204731/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204731/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/output_20250613T204542/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/output_20250613T204542/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/output_logging_20250613T204542/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/output_logging_20250613T204542/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204541/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204541/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/output_20250613T204354/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/output_20250613T204354/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/webWorker/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/webWorker/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/vscode.json-language-features/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/vscode.json-language-features/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/output_logging_20250613T204507/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/output_logging_20250613T204507/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/output_logging_20250613T204354/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/output_logging_20250613T204354/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T204353/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T204353/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/44f5d739/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/44f5d739/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/42cc0f23/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/42cc0f23/"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedProfilesData/__default__profile__/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedProfilesData/__default__profile__/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T213928/window2/exthost/vscode.json-language-features/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T213928/window2/exthost/vscode.json-language-features/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/augment.vscode-augment/augment-global-state/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/augment.vscode-augment/augment-global-state/"}}